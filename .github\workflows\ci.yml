name: CI/CD

on:
  push:
    branches: [ develop ]   # Chỉ chạy khi push develop

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3
      
      - name: Install pnpm
        run: npm install -g pnpm

      - uses: actions/setup-node@v4
        with:
          node-version: '22.x'
          cache: 'pnpm'

      - run: pnpm install
      - run: pnpm lint
      - run: pnpm build
