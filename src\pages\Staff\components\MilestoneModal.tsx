import React, { useState, useEffect, use<PERSON>allback, useMemo } from "react";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  CheckCircle,
  Clock,
  AlertTriangle,
  Calendar,
  Target,
  ChevronDown,
  ChevronRight,
  // X,
  Plus,
  Edit2,
  Trash2,
  Save,
  Info,
  FileText,
} from "lucide-react";
import {
  useMilestonesByProjectId,
  useCreateMilestone,
  useUpdateMilestone,
  useDeleteMilestone,
} from "@/hooks/queries/milestone";
import {
  useTasksByMilestoneId,
  useCreateTask,
  useUpdateTask,
  useDeleteTask,
} from "@/hooks/queries/task";
import { useProject, useUpdateProject } from "@/hooks/queries/project";
import { format } from "date-fns";
import { ProjectTask } from "@/types/task";
import { Milestone } from "@/types/milestone";
import { Proposal } from "@/types/project";
import { toast } from "sonner";
import { useBaseUserRoleId } from "@/hooks/queries";
import { Loading } from "@/components";
import { useQueryClient } from "@tanstack/react-query";

interface Task {
  id: string;
  title: string;
  description: string;
  status: "To Do" | "In Progress" | "Completed";
  priority: "Low" | "Medium" | "High";
  dueDate: string;
  assignedTo: string;
  createdAt: string;
  completedAt?: string;
  "start-date": string;
  "end-date": string;
  "member-tasks": string;
}

interface MilestoneModalProps {
  isOpen: boolean;
  onClose: () => void;
  projectId: string;
  projectName: string;
}

// Transform API milestone to component format
const transformMilestone = (apiMilestone: Milestone): Milestone => ({
  id: apiMilestone.id,
  code: apiMilestone.code,
  title: apiMilestone.title || `Milestone ${apiMilestone.code}`,
  description: apiMilestone.description || "",
  objective: apiMilestone.objective || "",
  cost: apiMilestone.cost,
  type: apiMilestone.type,
  status: transformMilestoneStatus(apiMilestone.status),
  "start-date": apiMilestone["start-date"] || "",
  "end-date": apiMilestone["end-date"] || "",
  "created-at": apiMilestone["created-at"] || "",
  "project-id": apiMilestone["project-id"],
  "creator-id": apiMilestone["creator-id"],
  project: apiMilestone.project,
  creator: apiMilestone.creator,
  evaluations: apiMilestone.evaluations,
  tasks: [],
});

// Transform milestone status
const transformMilestoneStatus = (
  status: string
): "Not Started" | "In Progress" | "Completed" | "Overdue" => {
  switch (status?.toLowerCase()) {
    case "in_progress":
    case "in-progress":
      return "In Progress";
    case "completed":
    case "complete":
      return "Completed";
    case "overdue":
      return "Overdue";
    default:
      return "Not Started";
  }
};

// Transform API task to component format
const transformTask = (apiTask: ProjectTask): Task => ({
  id: apiTask.id,
  title: apiTask.name,
  description: apiTask.description || "",
  status: transformTaskStatus(apiTask.status),
  priority: transformTaskPriority(apiTask.priority),
  dueDate: apiTask["end-date"] || "",
  assignedTo: "",
  createdAt: apiTask["start-date"] || "",
  completedAt: apiTask["delivery-date"] || undefined,
  "start-date": apiTask["start-date"] || "",
  "end-date": apiTask["end-date"] || "",
  "member-tasks": "",
});

// Transform task status
const transformTaskStatus = (
  status: string
): "To Do" | "In Progress" | "Completed" => {
  switch (status?.toLowerCase()) {
    case "in_progress":
    case "in-progress":
      return "In Progress";
    case "completed":
    case "complete":
      return "Completed";
    default:
      return "To Do";
  }
};

// Transform task priority
const transformTaskPriority = (
  priority: string | null
): "Low" | "Medium" | "High" => {
  if (!priority) return "Low";
  switch (priority.toLowerCase()) {
    case "high":
      return "High";
    case "medium":
      return "Medium";
    case "low":
    default:
      return "Low";
  }
};

// Utility function to format datetime-local input value
const formatDateTimeLocal = (dateString: string): string => {
  if (!dateString) return "";
  try {
    const date = new Date(dateString);
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    const hours = String(date.getHours()).padStart(2, "0");
    const minutes = String(date.getMinutes()).padStart(2, "0");
    return `${year}-${month}-${day}T${hours}:${minutes}`;
  } catch {
    return "";
  }
};

// Proposal Form Component
const ProposalForm: React.FC<{
  proposal?: Proposal;
  projectId: string;
  onSave: () => void;
  onCancel: () => void;
}> = ({ proposal, projectId, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    "english-title": proposal?.["english-title"] || "",
    "vietnamese-title": proposal?.["vietnamese-title"] || "",
    description: proposal?.description || "",
    "requirement-note": proposal?.["requirement-note"] || "",
    budget: proposal?.budget || 0,
    duration: proposal?.duration || 0,
    "maximum-member": proposal?.["maximum-member"] || 1,
    language: proposal?.language || "",
    category: proposal?.category || "",
    type: proposal?.type || "",
    genre: proposal?.genre || "",
    abbreviations: proposal?.abbreviations || "",
    "start-date": proposal?.["start-date"]
      ? proposal["start-date"].split("T")[0]
      : "",
    "end-date": proposal?.["end-date"]
      ? proposal["end-date"].split("T")[0]
      : "",
  });

  const [validationErrors, setValidationErrors] = useState<{
    startDate?: string;
    endDate?: string;
  }>({});

  const updateProjectMutation = useUpdateProject();

  // Validation function for proposal dates
  const validateProposalDates = useCallback(
    (startDate: string, endDate: string) => {
      const errors: { startDate?: string; endDate?: string } = {};
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

      if (startDate) {
        const startDateTime = new Date(startDate);
        if (startDateTime < today) {
          errors.startDate = "Start date cannot be in the past";
        }
      }

      if (endDate) {
        const endDateTime = new Date(endDate);
        if (endDateTime < today) {
          errors.endDate = "End date cannot be in the past";
        }
      }

      if (startDate && endDate) {
        const startDateTime = new Date(startDate);
        const endDateTime = new Date(endDate);

        if (endDateTime <= startDateTime) {
          errors.endDate = "End date must be after start date";
        }
      }

      return errors;
    },
    []
  );

  // Real-time validation when dates change
  const startDate = formData["start-date"];
  const endDate = formData["end-date"];

  useEffect(() => {
    const errors = validateProposalDates(startDate, endDate);
    setValidationErrors(errors);
  }, [startDate, endDate, validateProposalDates]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Final validation before submit
    const errors = validateProposalDates(
      formData["start-date"],
      formData["end-date"]
    );
    if (Object.keys(errors).length > 0) {
      toast.error("Please fix validation errors before submitting");
      return;
    }

    try {
      const submitData = {
        ...formData,
        "start-date": formData["start-date"]
          ? new Date(formData["start-date"]).toISOString()
          : null,
        "end-date": formData["end-date"]
          ? new Date(formData["end-date"]).toISOString()
          : null,
      };

      await updateProjectMutation.mutateAsync({
        projectId: projectId,
        data: submitData,
      });

      toast.success("Proposal updated successfully");
      onSave();
    } catch (error) {
      console.error("Error updating proposal:", error);
      toast.error("Failed to update proposal");
    }
  };

  const isFormValid = Object.keys(validationErrors).length === 0;

  return (
    <div className="max-h-[60vh] overflow-y-auto">
      <form onSubmit={handleSubmit} className="space-y-4 pr-2">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              English Title
            </label>
            <Input
              type="text"
              value={formData["english-title"]}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  "english-title": e.target.value,
                }))
              }
              required
              placeholder="Enter English title"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Vietnamese Title
            </label>
            <Input
              type="text"
              value={formData["vietnamese-title"]}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  "vietnamese-title": e.target.value,
                }))
              }
              required
              placeholder="Enter Vietnamese title"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Description
          </label>
          <Textarea
            value={formData.description}
            onChange={(e) =>
              setFormData((prev) => ({ ...prev, description: e.target.value }))
            }
            placeholder="Enter project description"
            rows={4}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Requirement Note
          </label>
          <Textarea
            value={formData["requirement-note"]}
            onChange={(e) =>
              setFormData((prev) => ({
                ...prev,
                "requirement-note": e.target.value,
              }))
            }
            placeholder="Enter requirement notes"
            rows={3}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Budget (VND)
            </label>
            <Input
              type="number"
              value={formData.budget}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  budget: Number(e.target.value),
                }))
              }
              min="0"
              placeholder="Enter budget"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Duration (months)
            </label>
            <Input
              type="number"
              value={formData.duration}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  duration: Number(e.target.value),
                }))
              }
              min="1"
              placeholder="Enter duration"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Maximum Members
            </label>
            <Input
              type="number"
              value={formData["maximum-member"]}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  "maximum-member": Number(e.target.value),
                }))
              }
              min="1"
              placeholder="Enter max members"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Language
            </label>
            <Select
              value={formData.language}
              onValueChange={(value) =>
                setFormData((prev) => ({ ...prev, language: value }))
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="Select language" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="vietnamese">Vietnamese</SelectItem>
                <SelectItem value="english">English</SelectItem>
                <SelectItem value="both">Both</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Category
            </label>
            <Input
              type="text"
              value={formData.category}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, category: e.target.value }))
              }
              placeholder="Enter category"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Type
            </label>
            <Input
              type="text"
              value={formData.type}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, type: e.target.value }))
              }
              placeholder="Enter type"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Genre
            </label>
            <Input
              type="text"
              value={formData.genre}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, genre: e.target.value }))
              }
              placeholder="Enter genre"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Abbreviations
            </label>
            <Input
              type="text"
              value={formData.abbreviations}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  abbreviations: e.target.value,
                }))
              }
              placeholder="Enter abbreviations"
            />
          </div>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Start Date
            </label>
            <Input
              type="date"
              value={formData["start-date"]}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  "start-date": e.target.value,
                }))
              }
              className={validationErrors.startDate ? "border-red-500" : ""}
            />
            {validationErrors.startDate && (
              <p className="text-red-500 text-xs mt-1">
                {validationErrors.startDate}
              </p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              End Date
            </label>
            <Input
              type="date"
              value={formData["end-date"]}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, "end-date": e.target.value }))
              }
              className={validationErrors.endDate ? "border-red-500" : ""}
            />
            {validationErrors.endDate && (
              <p className="text-red-500 text-xs mt-1">
                {validationErrors.endDate}
              </p>
            )}
          </div>
        </div>

        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={updateProjectMutation.isPending || !isFormValid}
          >
            {updateProjectMutation.isPending ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Updating...
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                Update Proposal
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};

// Milestone Form Component with updated validation
const MilestoneForm: React.FC<{
  milestone?: Milestone;
  projectId: string;
  onSave: () => void;
  onCancel: () => void;
}> = ({ milestone, projectId, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    title: milestone?.title || "",
    description: milestone?.description || "",
    objective: milestone?.objective || "",
    type: milestone?.type || "milestone",
    cost: milestone?.cost || 0,
    "start-date": milestone?.["start-date"]
      ? milestone["start-date"].split("T")[0]
      : "",
    "end-date": milestone?.["end-date"]
      ? milestone["end-date"].split("T")[0]
      : "",
  });

  const [validationErrors, setValidationErrors] = useState<{
    startDate?: string;
    endDate?: string;
  }>({});

  const { data: userRoleData } = useBaseUserRoleId();
  const createMilestoneMutation = useCreateMilestone();
  const updateMilestoneMutation = useUpdateMilestone();

  // Validation function for milestones
  const validateMilestoneDates = useCallback(
    (startDate: string, endDate: string) => {
      const errors: { startDate?: string; endDate?: string } = {};
      const now = new Date();
      const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

      if (startDate) {
        const startDateTime = new Date(startDate);
        if (startDateTime < today) {
          errors.startDate = "Start date cannot be in the past";
        }
      }

      if (endDate) {
        const endDateTime = new Date(endDate);
        if (endDateTime < today) {
          errors.endDate = "End date cannot be in the past";
        }
      }

      if (startDate && endDate) {
        const startDateTime = new Date(startDate);
        const endDateTime = new Date(endDate);

        // Check if end date is the same day as start date
        const isSameDay =
          startDateTime.toDateString() === endDateTime.toDateString();
        if (isSameDay) {
          errors.endDate = "End date cannot be the same day as start date";
        } else if (endDateTime <= startDateTime) {
          errors.endDate = "End date must be after start date";
        }
      }

      return errors;
    },
    []
  );

  // Real-time validation when dates change
  const milestoneStartDate = formData["start-date"];
  const milestoneEndDate = formData["end-date"];

  useEffect(() => {
    const errors = validateMilestoneDates(milestoneStartDate, milestoneEndDate);
    setValidationErrors(errors);
  }, [milestoneStartDate, milestoneEndDate, validateMilestoneDates]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!userRoleData?.data) {
      toast.error("User ID not found");
      return;
    }

    // Final validation before submit
    const errors = validateMilestoneDates(
      formData["start-date"],
      formData["end-date"]
    );
    if (Object.keys(errors).length > 0) {
      toast.error("Please fix validation errors before submitting");
      return;
    }

    try {
      const submitData = {
        ...formData,
        "project-id": projectId,
        "creator-id": userRoleData.data,
        "start-date": formData["start-date"]
          ? new Date(formData["start-date"]).toISOString()
          : "",
        "end-date": formData["end-date"]
          ? new Date(formData["end-date"]).toISOString()
          : "",
      };

      if (milestone) {
        await updateMilestoneMutation.mutateAsync({
          id: milestone.id,
          data: submitData,
        });
        toast.success("Milestone updated successfully");
      } else {
        await createMilestoneMutation.mutateAsync(submitData);
        toast.success("Milestone created successfully");
      }
      onSave();
    } catch (error) {
      console.error("Error saving milestone:", error);
      toast.error(`Failed to ${milestone ? "update" : "create"} milestone`);
    }
  };

  const isFormValid = Object.keys(validationErrors).length === 0;

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Title
        </label>
        <Input
          type="text"
          value={formData.title}
          onChange={(e) =>
            setFormData((prev) => ({ ...prev, title: e.target.value }))
          }
          required
          placeholder="Enter milestone title"
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Description
        </label>
        <Textarea
          value={formData.description}
          onChange={(e) =>
            setFormData((prev) => ({ ...prev, description: e.target.value }))
          }
          placeholder="Enter milestone description"
          rows={3}
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Objective
        </label>
        <Textarea
          value={formData.objective}
          onChange={(e) =>
            setFormData((prev) => ({ ...prev, objective: e.target.value }))
          }
          placeholder="Enter milestone objective"
          rows={2}
        />
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Cost (VND)
        </label>
        <Input
          type="number"
          value={formData.cost}
          onChange={(e) =>
            setFormData((prev) => ({
              ...prev,
              cost: Number(e.target.value) || 0,
            }))
          }
          min="0"
          placeholder="Enter milestone cost"
        />
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Start Date
          </label>
          <Input
            type="date"
            value={formData["start-date"]}
            onChange={(e) =>
              setFormData((prev) => ({ ...prev, "start-date": e.target.value }))
            }
            className={validationErrors.startDate ? "border-red-500" : ""}
          />
          {validationErrors.startDate && (
            <p className="text-red-500 text-xs mt-1">
              {validationErrors.startDate}
            </p>
          )}
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            End Date
          </label>
          <Input
            type="date"
            value={formData["end-date"]}
            onChange={(e) =>
              setFormData((prev) => ({ ...prev, "end-date": e.target.value }))
            }
            className={validationErrors.endDate ? "border-red-500" : ""}
          />
          {validationErrors.endDate && (
            <p className="text-red-500 text-xs mt-1">
              {validationErrors.endDate}
            </p>
          )}
        </div>
      </div>

      <div className="flex justify-end gap-2">
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={
            createMilestoneMutation.isPending ||
            updateMilestoneMutation.isPending ||
            !isFormValid
          }
        >
          {createMilestoneMutation.isPending ||
          updateMilestoneMutation.isPending ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Saving...
            </>
          ) : (
            <>
              <Save className="w-4 h-4 mr-2" />
              {milestone ? "Update" : "Create"}
            </>
          )}
        </Button>
      </div>
    </form>
  );
};

// Task Form Component with updated validation
const TaskForm: React.FC<{
  task?: Task;
  milestoneId: string;
  onSave: () => void;
  onCancel: () => void;
}> = ({ task, milestoneId, onSave, onCancel }) => {
  const [formData, setFormData] = useState({
    name: task?.title || "",
    description: task?.description || "",
    priority: task?.priority || "Low",
    "start-date": task?.["start-date"]
      ? formatDateTimeLocal(task["start-date"])
      : "",
    "end-date": task?.["end-date"] ? formatDateTimeLocal(task["end-date"]) : "",
    note: "",
    "meeting-url": "",
  });

  const [validationErrors, setValidationErrors] = useState<{
    startDate?: string;
    endDate?: string;
  }>({});

  const { data: userRoleData } = useBaseUserRoleId();
  const createMutation = useCreateTask();
  const updateMutation = useUpdateTask();

  // Validation function for tasks
  const validateTaskDates = useCallback(
    (startDate: string, endDate: string) => {
      const errors: { startDate?: string; endDate?: string } = {};
      const now = new Date();

      if (startDate) {
        const startDateTime = new Date(startDate);
        if (startDateTime < now) {
          errors.startDate = "Start date cannot be in the past";
        }
      }

      if (endDate) {
        const endDateTime = new Date(endDate);
        if (endDateTime < now) {
          errors.endDate = "End date cannot be in the past";
        }
      }

      if (startDate && endDate) {
        const startDateTime = new Date(startDate);
        const endDateTime = new Date(endDate);

        // For tasks: end date can be the same day as start date,
        // but cannot be the same time or before the start time
        if (endDateTime <= startDateTime) {
          errors.endDate = "End time must be after start time";
        }
      }

      return errors;
    },
    []
  );

  const taskStartDate = formData["start-date"];
  const taskEndDate = formData["end-date"];

  useEffect(() => {
    const errors = validateTaskDates(taskStartDate, taskEndDate);
    setValidationErrors(errors);
  }, [taskStartDate, taskEndDate, validateTaskDates]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!userRoleData?.data) {
      toast.error("User ID not found");
      return;
    }

    // Final validation before submit
    const errors = validateTaskDates(
      formData["start-date"],
      formData["end-date"]
    );
    if (Object.keys(errors).length > 0) {
      toast.error("Please fix validation errors before submitting");
      return;
    }

    try {
      if (task) {
        const updateData = {
          name: formData.name,
          description: formData.description,
          "start-date": formData["start-date"]
            ? new Date(formData["start-date"]).toISOString()
            : "",
          "end-date": formData["end-date"]
            ? new Date(formData["end-date"]).toISOString()
            : "",
          priority: formData.priority,
          progress: 0,
          overdue: 0,
          "meeting-url": formData["meeting-url"] || null,
          note: formData.note,
          "milestone-id": milestoneId,
          "member-tasks": null,
        };

        await updateMutation.mutateAsync({
          taskId: task.id,
          taskData: updateData,
        });
        toast.success("Task updated successfully");
      } else {
        const createData = {
          name: formData.name,
          description: formData.description,
          "start-date": formData["start-date"]
            ? new Date(formData["start-date"]).toISOString()
            : "",
          "end-date": formData["end-date"]
            ? new Date(formData["end-date"]).toISOString()
            : "",
          priority: formData.priority,
          progress: 0,
          overdue: 0,
          "meeting-url": formData["meeting-url"] || null,
          note: formData.note,
          "milestone-id": milestoneId,
          //   "creator-id": userRoleData.data,
        };

        await createMutation.mutateAsync(createData);
        toast.success("Task created successfully");
      }
      onSave();
    } catch (error) {
      console.error("Error saving task:", error);
      toast.error(`Failed to ${task ? "update" : "create"} task`);
    }
  };

  const isFormValid = Object.keys(validationErrors).length === 0;

  return (
    <div className="max-h-[60vh] overflow-y-auto">
      <form onSubmit={handleSubmit} className="space-y-4 pr-2">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Task Name
          </label>
          <Input
            type="text"
            value={formData.name}
            onChange={(e) =>
              setFormData((prev) => ({ ...prev, name: e.target.value }))
            }
            required
            placeholder="Enter task name"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Description
          </label>
          <Textarea
            value={formData.description}
            onChange={(e) =>
              setFormData((prev) => ({ ...prev, description: e.target.value }))
            }
            placeholder="Enter task description"
            rows={3}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Priority
          </label>
          <Select
            value={formData.priority}
            onValueChange={(value) =>
              setFormData((prev) => ({
                ...prev,
                priority: value as "Low" | "Medium" | "High",
              }))
            }
          >
            <SelectTrigger>
              <SelectValue placeholder="Select priority" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Low">Low</SelectItem>
              <SelectItem value="Medium">Medium</SelectItem>
              <SelectItem value="High">High</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Start Date & Time
            </label>
            <Input
              type="datetime-local"
              value={formData["start-date"]}
              onChange={(e) =>
                setFormData((prev) => ({
                  ...prev,
                  "start-date": e.target.value,
                }))
              }
              className={validationErrors.startDate ? "border-red-500" : ""}
            />
            {validationErrors.startDate && (
              <p className="text-red-500 text-xs mt-1">
                {validationErrors.startDate}
              </p>
            )}
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              End Date & Time
            </label>
            <Input
              type="datetime-local"
              value={formData["end-date"]}
              onChange={(e) =>
                setFormData((prev) => ({ ...prev, "end-date": e.target.value }))
              }
              className={validationErrors.endDate ? "border-red-500" : ""}
            />
            {validationErrors.endDate && (
              <p className="text-red-500 text-xs mt-1">
                {validationErrors.endDate}
              </p>
            )}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Meeting URL
          </label>
          <Input
            type="url"
            value={formData["meeting-url"]}
            onChange={(e) =>
              setFormData((prev) => ({
                ...prev,
                "meeting-url": e.target.value,
              }))
            }
            placeholder="Enter meeting URL (optional)"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Notes
          </label>
          <Textarea
            value={formData.note}
            onChange={(e) =>
              setFormData((prev) => ({ ...prev, note: e.target.value }))
            }
            placeholder="Enter additional notes"
            rows={2}
          />
        </div>

        <div className="flex justify-end gap-2 pt-4 border-t">
          <Button type="button" variant="outline" onClick={onCancel}>
            Cancel
          </Button>
          <Button
            type="submit"
            disabled={
              createMutation.isPending ||
              updateMutation.isPending ||
              !isFormValid
            }
          >
            {createMutation.isPending || updateMutation.isPending ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Saving...
              </>
            ) : (
              <>
                <Save className="w-4 h-4 mr-2" />
                {task ? "Update" : "Create"}
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};

// Individual milestone task fetcher component
const MilestoneTaskFetcher: React.FC<{
  milestoneId: string;
  onTasksLoaded: (milestoneId: string, tasks: Task[]) => void;
}> = ({ milestoneId, onTasksLoaded }) => {
  const {
    data: tasksData,
    isLoading,
    error,
  } = useTasksByMilestoneId(milestoneId, 1, 100);

  useEffect(() => {
    if (!isLoading) {
      if (tasksData?.data?.["data-list"]) {
        const transformedTasks = tasksData.data["data-list"].map(transformTask);
        onTasksLoaded(milestoneId, transformedTasks);
      } else {
        onTasksLoaded(milestoneId, []);
      }
    }
  }, [tasksData, isLoading, error, milestoneId, onTasksLoaded]);

  return null;
};

// Custom hook to manage tasks for multiple milestones
const useMilestonesTasks = (milestoneIds: string[]) => {
  const [tasksMap, setTasksMap] = useState<Record<string, Task[]>>({});
  const [loadingMap, setLoadingMap] = useState<Record<string, boolean>>({});

  const handleTasksLoaded = useCallback(
    (milestoneId: string, tasks: Task[]) => {
      setTasksMap((prev) => ({ ...prev, [milestoneId]: tasks }));
      setLoadingMap((prev) => ({ ...prev, [milestoneId]: false }));
    },
    []
  );

  useEffect(() => {
    setLoadingMap((prev) => {
      const updated = { ...prev };
      milestoneIds.forEach((id) => {
        if (!(id in updated)) {
          updated[id] = true;
        }
      });
      return updated;
    });

    setTasksMap((prev) => {
      const updated = { ...prev };
      milestoneIds.forEach((id) => {
        if (!(id in updated)) {
          updated[id] = [];
        }
      });
      return updated;
    });
  }, [milestoneIds]);

  return { tasksMap, loadingMap, handleTasksLoaded };
};

// Enhanced Milestone Card Component for Modal
const MilestoneCard: React.FC<{
  milestone: Milestone;
  tasks: Task[];
  progress: number;
  isLoading: boolean;
  onEdit: () => void;
  onDelete: () => void;
  onAddTask: () => void;
  onEditTask: (task: Task) => void;
  onDeleteTask: (task: Task) => void;
}> = ({
  milestone,
  tasks,
  isLoading,
  onEdit,
  onDelete,
  onAddTask,
  onEditTask,
  onDeleteTask,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Completed":
        return "bg-green-100 text-green-800 border-green-200";
      case "In Progress":
        return "bg-blue-100 text-blue-800 border-blue-200";
      case "Overdue":
        return "bg-red-100 text-red-800 border-red-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Completed":
        return <CheckCircle className="w-4 h-4" />;
      case "In Progress":
        return <Clock className="w-4 h-4" />;
      case "Overdue":
        return <AlertTriangle className="w-4 h-4" />;
      default:
        return <Target className="w-4 h-4" />;
    }
  };

  const formatDate = (dateString: string) => {
    if (!dateString) return "Not set";
    try {
      return format(new Date(dateString), "MMM dd, yyyy");
    } catch {
      return "Invalid date";
    }
  };

  return (
    <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
      <div className="p-4">
        <div className="flex items-center justify-between">
          <div
            className="flex-1 cursor-pointer hover:bg-gray-50 p-2 rounded-md -m-2"
            onClick={() => setIsExpanded(!isExpanded)}
          >
            <div className="flex justify-between items-start gap-2">
              <h3 className="text-base font-semibold text-gray-900 break-words whitespace-normal flex-1">
                {milestone.title}
              </h3>
              <span
                className={`px-3 py-1 rounded-lg text-xs font-medium border ${getStatusColor(
                  milestone.status
                )}`}
              >
                <div className="flex items-center gap-1">
                  {getStatusIcon(milestone.status)}
                  {milestone.status}
                </div>
              </span>
            </div>

            {milestone.description && (
              <p className="text-gray-600 mb-3 text-sm line-clamp-2">
                {milestone.description}
              </p>
            )}

            <div className="flex items-center gap-4 text-xs text-gray-500">
              <div className="flex items-center gap-1">
                <Calendar className="w-3 h-3" />
                <span>End: {formatDate(milestone["end-date"] || "")}</span>
              </div>
              <div className="flex items-center gap-1">
                <Target className="w-3 h-3" />
                <span>{tasks.length} tasks</span>
              </div>
              {milestone.cost && (
                <div className="flex items-center gap-1">
                  <span className="font-medium text-green-600">
                    {milestone.cost.toLocaleString()} VND
                  </span>
                </div>
              )}
            </div>
          </div>

          <div className="flex items-center gap-2 ml-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={onEdit}
              className="h-8 w-8 p-0"
            >
              <Edit2 className="w-4 h-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onDelete}
              className="h-8 w-8 p-0 text-red-500 hover:text-red-700"
            >
              <Trash2 className="w-4 h-4" />
            </Button>
            <div
              className="cursor-pointer"
              onClick={() => setIsExpanded(!isExpanded)}
            >
              {isExpanded ? (
                <ChevronDown className="w-4 h-4 text-gray-400" />
              ) : (
                <ChevronRight className="w-4 h-4 text-gray-400" />
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Expanded Content */}
      {isExpanded && (
        <div className="border-t border-gray-100 px-4 py-3">
          <div className="flex justify-between items-center mb-3">
            <h4 className="font-medium text-gray-900 text-sm">
              Tasks ({tasks.length})
            </h4>
            <Button size="sm" onClick={onAddTask} className="h-8">
              <Plus className="w-4 h-4 mr-1" />
              Add Task
            </Button>
          </div>

          {isLoading ? (
            <div className="flex items-center justify-center py-6">
              <Loading />
            </div>
          ) : tasks.length > 0 ? (
            <div className="space-y-2">
              {tasks.map((task) => (
                <div
                  key={task.id}
                  className="flex items-center justify-between px-3 py-2 bg-gray-50 rounded-md text-sm"
                >
                  <div className="flex-1">
                    <h5 className="font-normal text-gray-900 text-sm">
                      {task.title}
                    </h5>
                    {task.description && (
                      <p className="text-xs text-gray-500 mt-0.5 line-clamp-1">
                        {task.description}
                      </p>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <span
                      className={`px-2 py-0.5 rounded text-xs font-medium ${
                        task.status === "Completed"
                          ? "bg-green-100 text-green-800"
                          : task.status === "In Progress"
                          ? "bg-blue-100 text-blue-800"
                          : "bg-gray-100 text-gray-800"
                      }`}
                    >
                      {task.status}
                    </span>
                    <span
                      className={`px-2 py-0.5 rounded text-xs font-medium ${
                        task.priority === "High"
                          ? "bg-red-100 text-red-800"
                          : task.priority === "Medium"
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-gray-100 text-gray-800"
                      }`}
                    >
                      {task.priority}
                    </span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onEditTask(task)}
                      className="h-6 w-6 p-0"
                    >
                      <Edit2 className="w-3 h-3" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onDeleteTask(task)}
                      className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
                    >
                      <Trash2 className="w-3 h-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-6 text-gray-500">
              <Target className="w-6 h-6 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No tasks found for this milestone</p>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

const MilestoneModal: React.FC<MilestoneModalProps> = ({
  isOpen,
  onClose,
  projectId,
  projectName,
}) => {
  // Tab state
  const [activeTab, setActiveTab] = useState("proposal");

  // Milestone states
  const [editingMilestone, setEditingMilestone] = useState<Milestone | null>(
    null
  );
  const [creatingMilestone, setCreatingMilestone] = useState(false);
  const [editingTask, setEditingTask] = useState<{
    task: Task | null;
    milestoneId: string;
  } | null>(null);
  const [creatingTask, setCreatingTask] = useState<string | null>(null);
  const [hasCreatedMeetingMilestone, setHasCreatedMeetingMilestone] =
    useState(false);

  // Proposal states
  const [editingProposal, setEditingProposal] = useState(false);

  const queryClient = useQueryClient();

  // API hooks
  const { data: milestonesData } = useMilestonesByProjectId(projectId);
  const { data: projectData } = useProject(projectId);
  const { data: userRoleData } = useBaseUserRoleId();
  const createMilestoneMutation = useCreateMilestone();
  const deleteMilestoneMutation = useDeleteMilestone();
  const deleteTaskMutation = useDeleteTask();

  // useEffect(() => {
  //   if (!isOpen || !milestonesData?.data || !userRoleData?.data) {
  //     return;
  //   }

  //   const apiMilestones = milestonesData.data;

  //   // Check if Meeting milestone exists (case-insensitive, trimmed)
  //   const hasMeetingMilestone = apiMilestones.some(
  //     (milestone) => milestone.title?.trim().toLowerCase() === "meeting"
  //   );

  //   if (!hasMeetingMilestone) {
  //     // Auto-create Meeting milestone
  //     const createMeetingMilestone = async () => {
  //       try {
  //         const meetingMilestoneData = {
  //           title: "Meeting",
  //           description: "Meeting Schedule",
  //           type: "normal",
  //           "project-id": projectId,
  //           "creator-id": userRoleData.data,
  //         };

  //         await createMilestoneMutation.mutateAsync(meetingMilestoneData);
  //         console.log("Meeting milestone created automatically");
  //       } catch (error) {
  //         console.error("Error auto-creating Meeting milestone:", error);
  //         // Don't show toast error to avoid interrupting user experience
  //       }
  //     };

  //     createMeetingMilestone();
  //   }
  // }, [
  //   isOpen,
  //   milestonesData?.data,
  //   userRoleData?.data,
  //   projectId,
  //   createMilestoneMutation,
  // ]);

  // Extract milestones from API response

  useEffect(() => {
    // Chỉ chạy khi modal mở, có data milestone, có user data, và chưa tạo Meeting milestone
    if (
      !isOpen ||
      !milestonesData?.data ||
      !userRoleData?.data ||
      hasCreatedMeetingMilestone
    ) {
      return;
    }

    const apiMilestones = milestonesData.data;

    // Check if Meeting milestone exists (case-insensitive, trimmed)
    const hasMeetingMilestone = apiMilestones.some(
      (milestone) => milestone.title?.trim().toLowerCase() === "meeting"
    );

    if (!hasMeetingMilestone) {
      // Set flag để tránh tạo lại
      setHasCreatedMeetingMilestone(true);

      // Auto-create Meeting milestone
      const createMeetingMilestone = async () => {
        try {
          const meetingMilestoneData = {
            title: "Meeting",
            description: "Meeting Schedule",
            type: "normal",
            "project-id": projectId,
            "creator-id": userRoleData.data,
          };

          await createMilestoneMutation.mutateAsync(meetingMilestoneData);

          // Refresh milestone data sau khi tạo
          queryClient.invalidateQueries({
            queryKey: ["milestones", projectId],
          });

          console.log("Meeting milestone created automatically");
        } catch (error) {
          console.error("Error auto-creating Meeting milestone:", error);
          // Reset flag nếu tạo thất bại để có thể thử lại
          setHasCreatedMeetingMilestone(false);
        }
      };

      createMeetingMilestone();
    } else {
      // Nếu đã có Meeting milestone thì set flag = true
      setHasCreatedMeetingMilestone(true);
    }
  }, [
    isOpen,
    milestonesData?.data,
    userRoleData?.data,
    projectId,
    hasCreatedMeetingMilestone,
    createMilestoneMutation,
    queryClient,
    hasCreatedMeetingMilestone,
    createMilestoneMutation,
    queryClient,
  ]);
  const apiMilestones = useMemo(
    () => milestonesData?.data || [],
    [milestonesData?.data]
  );

  // Transform milestones to component format
  const milestones = useMemo(() => {
    return apiMilestones.map(transformMilestone);
  }, [apiMilestones]);

  // Get milestone IDs for task fetching
  const milestoneIds = useMemo(() => milestones.map((m) => m.id), [milestones]);

  // Fetch tasks for all milestones
  const {
    tasksMap: milestoneTasksMap,
    loadingMap,
    handleTasksLoaded,
  } = useMilestonesTasks(milestoneIds);

  // Enhanced milestones with tasks
  const milestonesWithTasks = useMemo(() => {
    return milestones.map((milestone) => {
      const tasks = milestoneTasksMap[milestone.id] || [];
      const completedTasks = tasks.filter(
        (t) => t.status === "Completed"
      ).length;
      const progress =
        tasks.length > 0
          ? Math.round((completedTasks / tasks.length) * 100)
          : 0;

      return {
        ...milestone,
        tasks,
        progress,
      };
    });
  }, [milestones, milestoneTasksMap]);

  // Statistics calculations
  const completedMilestones = milestonesWithTasks.filter(
    (m) => m.status === "Completed"
  ).length;
  const inProgressMilestones = milestonesWithTasks.filter(
    (m) => m.status === "In Progress"
  ).length;
  const notStartedMilestones = milestonesWithTasks.filter(
    (m) => m.status === "Not Started"
  ).length;
  const totalTasks = milestonesWithTasks.reduce(
    (total, m) => total + (m.tasks?.length || 0),
    0
  );
  const totalCost = milestonesWithTasks.reduce((total, m) => {
    // Exclude Meeting milestone from cost calculation
    if (m.title?.toLowerCase().trim() === "meeting") {
      return total;
    }
    return total + (m.cost || 0);
  }, 0);

  // Handle milestone operations
  const handleCreateMilestone = () => {
    setCreatingMilestone(true);
  };

  const handleEditMilestone = (milestone: Milestone) => {
    setEditingMilestone(milestone);
  };

  const handleDeleteMilestone = async (milestone: Milestone) => {
    if (
      window.confirm(`Are you sure you want to delete "${milestone.title}"?`)
    ) {
      try {
        await deleteMilestoneMutation.mutateAsync({
          id: milestone.id,
          projectId: projectId,
        });
        toast.success("Milestone deleted successfully");
      } catch (error) {
        console.error("Error deleting milestone:", error);
        toast.error("Failed to delete milestone");
      }
    }
  };

  // Handle task operations
  const handleCreateTask = (milestoneId: string) => {
    setCreatingTask(milestoneId);
  };

  const handleEditTask = (task: Task, milestoneId: string) => {
    setEditingTask({ task, milestoneId });
  };

  const handleDeleteTask = async (task: Task) => {
    if (window.confirm(`Are you sure you want to delete "${task.title}"?`)) {
      try {
        await deleteTaskMutation.mutateAsync(task.id);
        toast.success("Task deleted successfully");
      } catch (error) {
        console.error("Error deleting task:", error);
        toast.error("Failed to delete task");
      }
    }
  };

  // Handle proposal operations
  const handleEditProposal = () => {
    setEditingProposal(true);
  };

  // Handle form submissions
  const handleMilestoneFormSave = () => {
    setEditingMilestone(null);
    setCreatingMilestone(false);
  };

  const handleTaskFormSave = () => {
    setEditingTask(null);
    setCreatingTask(null);
  };

  const handleProposalFormSave = () => {
    setEditingProposal(false);
  };

  // Reset forms when modal closes
  const handleClose = () => {
    setActiveTab("proposal");
    setEditingMilestone(null);
    setCreatingMilestone(false);
    setEditingTask(null);
    setCreatingTask(null);
    setEditingProposal(false);
    setEditingProposal(false);
    setHasCreatedMeetingMilestone(false); // Reset flag khi đóng modal
    onClose();
  };

  // Check if any form is open
  const isFormOpen =
    creatingMilestone ||
    editingMilestone ||
    creatingTask ||
    editingTask ||
    editingProposal;

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Target className="h-6 w-6 text-blue-600" />
              <div>
                <DialogTitle className="text-xl font-bold">
                  Project Management
                </DialogTitle>
                <p className="text-sm text-gray-600 mt-1">{projectName}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {!isFormOpen && activeTab === "milestones" && (
                <Button onClick={handleCreateMilestone} size="sm">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Milestone
                </Button>
              )}
              {!isFormOpen && activeTab === "proposal" && (
                <Button onClick={handleEditProposal} size="sm">
                  <Edit2 className="w-4 h-4 mr-2" />
                  Edit Proposal
                </Button>
              )}
            </div>
          </div>
        </DialogHeader>

        <div className="flex-1 flex flex-col gap-4 min-h-0">
          {/* Task fetchers - invisible components that handle data fetching */}
          {milestoneIds.map((milestoneId) => (
            <MilestoneTaskFetcher
              key={milestoneId}
              milestoneId={milestoneId}
              onTasksLoaded={handleTasksLoaded}
            />
          ))}

          {/* Tabs */}
          {!isFormOpen && (
            <Tabs
              value={activeTab}
              onValueChange={setActiveTab}
              className="flex-1 flex flex-col min-h-0"
            >
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger
                  value="proposal"
                  className="flex items-center gap-2"
                >
                  <FileText className="w-4 h-4" />
                  Proposal Information
                </TabsTrigger>
                <TabsTrigger
                  value="milestones"
                  className="flex items-center gap-2"
                >
                  <Target className="w-4 h-4" />
                  Milestones & Tasks
                </TabsTrigger>
              </TabsList>

              <TabsContent
                value="milestones"
                className="flex-1 flex flex-col mt-4 min-h-0"
              >
                {/* Milestones Content */}
                {/* Statistics Cards */}
                <div className="flex-shrink-0 grid grid-cols-2 lg:grid-cols-5 gap-3 mb-4">
                  <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="w-4 h-4 text-green-600" />
                      <div>
                        <p className="text-xl font-bold text-green-900">
                          {completedMilestones}
                        </p>
                        <p className="text-xs text-green-700">Completed</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4 text-blue-600" />
                      <div>
                        <p className="text-xl font-bold text-blue-900">
                          {inProgressMilestones}
                        </p>
                        <p className="text-xs text-blue-700">In Progress</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-orange-50 rounded-lg p-4 border border-orange-200">
                    <div className="flex items-center gap-2">
                      <AlertTriangle className="w-4 h-4 text-orange-600" />
                      <div>
                        <p className="text-xl font-bold text-orange-900">
                          {notStartedMilestones}
                        </p>
                        <p className="text-xs text-orange-700">Not Started</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-purple-50 rounded-lg p-4 border border-purple-200">
                    <div className="flex items-center gap-2">
                      <Target className="w-4 h-4 text-purple-600" />
                      <div>
                        <p className="text-xl font-bold text-purple-900">
                          {totalTasks}
                        </p>
                        <p className="text-xs text-purple-700">Total Tasks</p>
                      </div>
                    </div>
                  </div>

                  <div className="bg-emerald-50 rounded-lg p-4 border border-emerald-200">
                    <div className="flex items-center gap-2">
                      <span className="text-emerald-600 font-bold">₫</span>
                      <div>
                        <p className="text-xl font-bold text-emerald-900">
                          {(totalCost / 1000000).toFixed(1)}M
                        </p>
                        <p className="text-xs text-emerald-700">Total Cost</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Milestones List */}
                <div className="flex-1 overflow-y-auto">
                  <div className="space-y-3 pr-2">
                    {milestones.length > 0 ? (
                      milestones.map((milestone) => {
                        const tasks = milestoneTasksMap[milestone.id] || [];
                        const isLoading = loadingMap[milestone.id] ?? true;
                        const progress =
                          tasks.length > 0
                            ? Math.round(
                                (tasks.filter((t) => t.status === "Completed")
                                  .length /
                                  tasks.length) *
                                  100
                              )
                            : 0;

                        return (
                          <MilestoneCard
                            key={milestone.id}
                            milestone={milestone}
                            tasks={tasks}
                            progress={progress}
                            isLoading={isLoading}
                            onEdit={() => handleEditMilestone(milestone)}
                            onDelete={() => handleDeleteMilestone(milestone)}
                            onAddTask={() => handleCreateTask(milestone.id)}
                            onEditTask={(task) =>
                              handleEditTask(task, milestone.id)
                            }
                            onDeleteTask={handleDeleteTask}
                          />
                        );
                      })
                    ) : (
                      <div className="text-center py-12 bg-gray-50 rounded-lg">
                        <div className="max-w-md mx-auto">
                          <div className="p-3 bg-gray-200 rounded-full w-12 h-12 mx-auto mb-4 flex items-center justify-center">
                            <Clock className="w-6 h-6 text-gray-400" />
                          </div>
                          <h3 className="text-base font-semibold text-gray-900 mb-2">
                            No milestones found
                          </h3>
                          <p className="text-sm text-gray-600 mb-4">
                            Milestones will appear here once they are created
                            for this project.
                          </p>
                          <Button onClick={handleCreateMilestone}>
                            <Plus className="w-4 h-4 mr-2" />
                            Create First Milestone
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </TabsContent>

              <TabsContent
                value="proposal"
                className="flex-1 flex flex-col mt-4 min-h-0"
              >
                {/* Proposal Content */}
                {projectData?.data ? (
                  <div className="flex-1 overflow-y-auto pr-2 min-h-0">
                    <div className="bg-white rounded-lg border p-6 space-y-6">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Info className="w-5 h-5 text-blue-600" />
                          <h3 className="text-lg font-semibold">
                            Proposal Details
                          </h3>
                        </div>
                        <Badge variant="secondary">
                          Status: {projectData.data["project-detail"].status}
                        </Badge>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-4">
                          <div>
                            <label className="text-sm font-medium text-gray-700">
                              English Title
                            </label>
                            <p className="mt-1 text-sm text-gray-900">
                              {
                                projectData.data["project-detail"][
                                  "english-title"
                                ]
                              }
                            </p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-700">
                              Vietnamese Title
                            </label>
                            <p className="mt-1 text-sm text-gray-900">
                              {
                                projectData.data["project-detail"][
                                  "vietnamese-title"
                                ]
                              }
                            </p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-700">
                              Category
                            </label>
                            <p className="mt-1 text-sm text-gray-900">
                              {projectData.data["project-detail"].category}
                            </p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-700">
                              Type
                            </label>
                            <p className="mt-1 text-sm text-gray-900">
                              {projectData.data["project-detail"].type}
                            </p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-700">
                              Genre
                            </label>
                            <p className="mt-1 text-sm text-gray-900">
                              {projectData.data["project-detail"].genre}
                            </p>
                          </div>
                        </div>

                        <div className="space-y-4">
                          <div>
                            <label className="text-sm font-medium text-gray-700">
                              Budget
                            </label>
                            <p className="mt-1 text-sm text-gray-900">
                              {projectData.data[
                                "project-detail"
                              ].budget?.toLocaleString()}{" "}
                              VND
                            </p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-700">
                              Duration
                            </label>
                            <p className="mt-1 text-sm text-gray-900">
                              {projectData.data["project-detail"].duration}{" "}
                              months
                            </p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-700">
                              Maximum Members
                            </label>
                            <p className="mt-1 text-sm text-gray-900">
                              {
                                projectData.data["project-detail"][
                                  "maximum-member"
                                ]
                              }
                            </p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-700">
                              Language
                            </label>
                            <p className="mt-1 text-sm text-gray-900 capitalize">
                              {projectData.data["project-detail"].language}
                            </p>
                          </div>
                          <div>
                            <label className="text-sm font-medium text-gray-700">
                              Progress
                            </label>
                            <p className="mt-1 text-sm text-gray-900">
                              {projectData.data["project-detail"].progress}%
                            </p>
                          </div>
                        </div>
                      </div>

                      {projectData.data["project-detail"].description && (
                        <div>
                          <label className="text-sm font-medium text-gray-700">
                            Description
                          </label>
                          <p className="mt-1 text-sm text-gray-900 whitespace-pre-wrap">
                            {projectData.data["project-detail"].description}
                          </p>
                        </div>
                      )}

                      {projectData.data["project-detail"][
                        "requirement-note"
                      ] && (
                        <div>
                          <label className="text-sm font-medium text-gray-700">
                            Requirement Note
                          </label>
                          <p className="mt-1 text-sm text-gray-900 whitespace-pre-wrap">
                            {
                              projectData.data["project-detail"][
                                "requirement-note"
                              ]
                            }
                          </p>
                        </div>
                      )}

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4 border-t">
                        <div>
                          <label className="text-sm font-medium text-gray-700">
                            Start Date
                          </label>
                          <p className="mt-1 text-sm text-gray-900">
                            {projectData.data["project-detail"]["start-date"]
                              ? format(
                                  new Date(
                                    projectData.data["project-detail"][
                                      "start-date"
                                    ]
                                  ),
                                  "MMM dd, yyyy"
                                )
                              : "Not set"}
                          </p>
                        </div>
                        <div>
                          <label className="text-sm font-medium text-gray-700">
                            End Date
                          </label>
                          <p className="mt-1 text-sm text-gray-900">
                            {projectData.data["project-detail"]["end-date"]
                              ? format(
                                  new Date(
                                    projectData.data["project-detail"][
                                      "end-date"
                                    ]
                                  ),
                                  "MMM dd, yyyy"
                                )
                              : "Not set"}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="flex-1 flex items-center justify-center">
                    <Loading />
                  </div>
                )}
              </TabsContent>
            </Tabs>
          )}

          {/* Forms */}
          {creatingMilestone && (
            <div className="flex-shrink-0 bg-gray-50 rounded-lg p-4 border">
              <h3 className="text-lg font-semibold mb-4">
                Create New Milestone
              </h3>
              <MilestoneForm
                projectId={projectId}
                onSave={handleMilestoneFormSave}
                onCancel={() => setCreatingMilestone(false)}
              />
            </div>
          )}

          {editingMilestone && (
            <div className="flex-shrink-0 bg-gray-50 rounded-lg p-4 border">
              <h3 className="text-lg font-semibold mb-4">Edit Milestone</h3>
              <MilestoneForm
                milestone={editingMilestone}
                projectId={projectId}
                onSave={handleMilestoneFormSave}
                onCancel={() => setEditingMilestone(null)}
              />
            </div>
          )}

          {creatingTask && (
            <div className="flex-shrink-0 bg-gray-50 rounded-lg p-4 border">
              <h3 className="text-lg font-semibold mb-4">Create New Task</h3>
              <TaskForm
                milestoneId={creatingTask}
                onSave={handleTaskFormSave}
                onCancel={() => setCreatingTask(null)}
              />
            </div>
          )}

          {editingTask && (
            <div className="flex-shrink-0 bg-gray-50 rounded-lg p-4 border">
              <h3 className="text-lg font-semibold mb-4">Edit Task</h3>
              <TaskForm
                task={editingTask.task ?? undefined}
                milestoneId={editingTask.milestoneId}
                onSave={handleTaskFormSave}
                onCancel={() => setEditingTask(null)}
              />
            </div>
          )}

          {editingProposal && (
            <div className="flex-shrink-0 bg-gray-50 rounded-lg p-4 border">
              <h3 className="text-lg font-semibold mb-4">Edit Proposal</h3>
              <ProposalForm
                proposal={projectData?.data?.["project-detail"] as Proposal}
                projectId={projectId}
                onSave={handleProposalFormSave}
                onCancel={() => setEditingProposal(false)}
              />
            </div>
          )}

          {/* Forms */}
          {creatingMilestone && (
            <div className="flex-shrink-0 bg-gray-50 rounded-lg p-4 border">
              <h3 className="text-lg font-semibold mb-4">
                Create New Milestone
              </h3>
              <MilestoneForm
                projectId={projectId}
                onSave={handleMilestoneFormSave}
                onCancel={() => setCreatingMilestone(false)}
              />
            </div>
          )}

          {editingMilestone && (
            <div className="flex-shrink-0 bg-gray-50 rounded-lg p-4 border">
              <h3 className="text-lg font-semibold mb-4">Edit Milestone</h3>
              <MilestoneForm
                milestone={editingMilestone}
                projectId={projectId}
                onSave={handleMilestoneFormSave}
                onCancel={() => setEditingMilestone(null)}
              />
            </div>
          )}

          {creatingTask && (
            <div className="flex-shrink-0 bg-gray-50 rounded-lg p-4 border">
              <h3 className="text-lg font-semibold mb-4">Create New Task</h3>
              <TaskForm
                milestoneId={creatingTask}
                onSave={handleTaskFormSave}
                onCancel={() => setCreatingTask(null)}
              />
            </div>
          )}

          {editingTask && (
            <div className="flex-shrink-0 bg-gray-50 rounded-lg p-4 border">
              <h3 className="text-lg font-semibold mb-4">Edit Task</h3>
              <TaskForm
                task={editingTask.task ?? undefined}
                milestoneId={editingTask.milestoneId}
                onSave={handleTaskFormSave}
                onCancel={() => setEditingTask(null)}
              />
            </div>
          )}

          {editingProposal && (
            <div className="flex-shrink-0 bg-gray-50 rounded-lg p-4 border">
              <h3 className="text-lg font-semibold mb-4">Edit Proposal</h3>
              <ProposalForm
                proposal={projectData?.data?.["project-detail"] as Proposal}
                projectId={projectId}
                onSave={handleProposalFormSave}
                onCancel={() => setEditingProposal(false)}
              />
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default MilestoneModal;
